"""
Configuration settings for the Transport Pass OCR API.
"""

from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Application Configuration
    app_name: str = "Transport Pass OCR API"
    app_version: str = "1.0.0"
    debug: bool = False
    log_level: str = "INFO"
    
    # Server Configuration
    host: str = "0.0.0.0"
    port: int = 8000
    
    # OCR Configuration
    tesseract_cmd_path: str = "tesseract"
    ocr_confidence_threshold: float = 0.6
    image_max_size_mb: int = 10
    
    # Processing Configuration
    max_workers: int = 4
    processing_timeout_seconds: int = 30
    
    # Validation Thresholds
    document_validation_confidence_threshold: float = 0.7
    text_similarity_threshold: float = 0.8
    
    # File Upload Configuration
    max_file_size: int = image_max_size_mb * 1024 * 1024  # Convert MB to bytes
    allowed_extensions: list = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif"]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings
