"""
Image processing utilities for Transport Pass OCR.
Handles image preprocessing, rotation correction, and format conversion.
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from typing import Tuple, Optional, Union
import io
import math
from loguru import logger


class ImageProcessor:
    """Image processing utilities for OCR preprocessing."""
    
    def __init__(self):
        """Initialize the image processor."""
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
    
    def load_image_from_bytes(self, image_bytes: bytes) -> np.ndarray:
        """
        Load image from bytes and convert to OpenCV format.
        
        Args:
            image_bytes: Raw image bytes
            
        Returns:
            OpenCV image array (BGR format)
        """
        try:
            # Convert bytes to PIL Image
            pil_image = Image.open(io.BytesIO(image_bytes))
            
            # Convert to RGB if necessary
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # Convert PIL to OpenCV format (RGB to BGR)
            cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            logger.info(f"Image loaded successfully. Shape: {cv_image.shape}")
            return cv_image
            
        except Exception as e:
            logger.error(f"Error loading image from bytes: {str(e)}")
            raise ValueError(f"Failed to load image: {str(e)}")
    
    def detect_rotation_angle(self, image: np.ndarray) -> float:
        """
        Detect rotation angle of the document using Hough line detection.
        
        Args:
            image: OpenCV image array
            
        Returns:
            Rotation angle in degrees
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply edge detection
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            
            # Detect lines using Hough transform
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
            
            if lines is None:
                logger.warning("No lines detected for rotation correction")
                return 0.0
            
            # Calculate angles of detected lines
            angles = []
            for rho, theta in lines[:, 0]:
                angle = theta * 180 / np.pi
                # Convert to rotation angle
                if angle > 90:
                    angle = angle - 180
                angles.append(angle)
            
            # Find the most common angle (mode)
            if angles:
                # Use median as a robust estimate
                rotation_angle = np.median(angles)
                logger.info(f"Detected rotation angle: {rotation_angle:.2f} degrees")
                return rotation_angle
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error detecting rotation angle: {str(e)}")
            return 0.0
    
    def rotate_image(self, image: np.ndarray, angle: float) -> np.ndarray:
        """
        Rotate image by the specified angle.
        
        Args:
            image: OpenCV image array
            angle: Rotation angle in degrees
            
        Returns:
            Rotated image
        """
        try:
            if abs(angle) < 0.5:  # Skip rotation for very small angles
                return image
            
            height, width = image.shape[:2]
            center = (width // 2, height // 2)
            
            # Get rotation matrix
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            
            # Calculate new dimensions to avoid cropping
            cos_angle = abs(rotation_matrix[0, 0])
            sin_angle = abs(rotation_matrix[0, 1])
            new_width = int((height * sin_angle) + (width * cos_angle))
            new_height = int((height * cos_angle) + (width * sin_angle))
            
            # Adjust rotation matrix for new dimensions
            rotation_matrix[0, 2] += (new_width / 2) - center[0]
            rotation_matrix[1, 2] += (new_height / 2) - center[1]
            
            # Perform rotation
            rotated = cv2.warpAffine(
                image, rotation_matrix, (new_width, new_height),
                flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_CONSTANT,
                borderValue=(255, 255, 255)
            )
            
            logger.info(f"Image rotated by {angle:.2f} degrees")
            return rotated
            
        except Exception as e:
            logger.error(f"Error rotating image: {str(e)}")
            return image
    
    def enhance_image_quality(self, image: np.ndarray) -> np.ndarray:
        """
        Enhance image quality for better OCR results.
        
        Args:
            image: OpenCV image array
            
        Returns:
            Enhanced image
        """
        try:
            # Convert to PIL for enhancement
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(1.5)
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.2)
            
            # Convert back to OpenCV format
            enhanced = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            logger.info("Image quality enhanced")
            return enhanced
            
        except Exception as e:
            logger.error(f"Error enhancing image quality: {str(e)}")
            return image
    
    def denoise_image(self, image: np.ndarray) -> np.ndarray:
        """
        Remove noise from the image.

        Args:
            image: OpenCV image array

        Returns:
            Denoised image
        """
        try:
            # Apply Non-local Means Denoising
            denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)

            logger.info("Image denoised")
            return denoised

        except Exception as e:
            logger.error(f"Error denoising image: {str(e)}")
            return image

    def correct_skew(self, image: np.ndarray) -> np.ndarray:
        """
        Correct skew in the document image.

        Args:
            image: OpenCV image array

        Returns:
            Skew-corrected image
        """
        try:
            # Detect rotation angle
            angle = self.detect_rotation_angle(image)

            # Correct rotation if significant
            if abs(angle) > 0.5:
                corrected = self.rotate_image(image, -angle)
                logger.info(f"Skew corrected by {-angle:.2f} degrees")
                return corrected

            return image

        except Exception as e:
            logger.error(f"Error correcting skew: {str(e)}")
            return image

    def resize_image(self, image: np.ndarray, max_width: int = 2000, max_height: int = 2000) -> np.ndarray:
        """
        Resize image while maintaining aspect ratio.

        Args:
            image: OpenCV image array
            max_width: Maximum width
            max_height: Maximum height

        Returns:
            Resized image
        """
        try:
            height, width = image.shape[:2]

            # Calculate scaling factor
            scale_w = max_width / width
            scale_h = max_height / height
            scale = min(scale_w, scale_h, 1.0)  # Don't upscale

            if scale < 1.0:
                new_width = int(width * scale)
                new_height = int(height * scale)

                resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
                logger.info(f"Image resized from {width}x{height} to {new_width}x{new_height}")
                return resized

            return image

        except Exception as e:
            logger.error(f"Error resizing image: {str(e)}")
            return image

    def preprocess_for_ocr(self, image_bytes: bytes) -> np.ndarray:
        """
        Complete preprocessing pipeline for OCR.

        Args:
            image_bytes: Raw image bytes

        Returns:
            Preprocessed image ready for OCR
        """
        try:
            logger.info("Starting image preprocessing pipeline")

            # Load image
            image = self.load_image_from_bytes(image_bytes)

            # Resize if too large
            image = self.resize_image(image)

            # Denoise
            image = self.denoise_image(image)

            # Correct skew
            image = self.correct_skew(image)

            # Enhance quality
            image = self.enhance_image_quality(image)

            logger.info("Image preprocessing completed successfully")
            return image

        except Exception as e:
            logger.error(f"Error in preprocessing pipeline: {str(e)}")
            raise

    def convert_to_grayscale(self, image: np.ndarray) -> np.ndarray:
        """
        Convert image to grayscale for OCR processing.

        Args:
            image: OpenCV image array

        Returns:
            Grayscale image
        """
        try:
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                logger.info("Image converted to grayscale")
                return gray
            return image

        except Exception as e:
            logger.error(f"Error converting to grayscale: {str(e)}")
            return image
