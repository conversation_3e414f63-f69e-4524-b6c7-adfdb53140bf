"""
Simple test script for the Transport Pass OCR API.
"""

import requests
import json
from PIL import Image, ImageDraw, ImageFont
import io
import os


def create_test_image():
    """Create a simple test image that looks like a Transport Pass."""
    # Create a white image
    width, height = 800, 600
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font_large = ImageFont.truetype("arial.ttf", 24)
        font_medium = ImageFont.truetype("arial.ttf", 16)
        font_small = ImageFont.truetype("arial.ttf", 12)
    except:
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Draw header
    draw.text((50, 30), "DEPARTMENT OF EXCISE", fill='black', font=font_large)
    draw.text((50, 60), "UTTAR PRADESH", fill='black', font=font_large)
    draw.text((50, 100), "TRANSPORT PASS", fill='black', font=font_medium)
    
    # Draw some form fields
    draw.text((50, 150), "Transport Pass No: WHOLESALE10601-FL2B-15232", fill='black', font=font_small)
    draw.text((50, 180), "Indent No: IND123456", fill='black', font=font_small)
    draw.text((50, 210), "Issue Date: 15/01/2024", fill='black', font=font_small)
    draw.text((50, 240), "Unit Name: Test Unit", fill='black', font=font_small)
    draw.text((50, 270), "License Type: FL2B", fill='black', font=font_small)
    
    # Draw a simple rectangle to simulate a QR code
    draw.rectangle([650, 50, 750, 150], outline='black', width=2)
    draw.text((660, 160), "QR Code", fill='black', font=font_small)
    
    # Save to bytes
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format='JPEG')
    img_byte_arr.seek(0)
    
    return img_byte_arr.getvalue()


def test_health_endpoint():
    """Test the health endpoint."""
    print("Testing health endpoint...")
    try:
        response = requests.get('http://localhost:8000/health')
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False


def test_validate_document():
    """Test the document validation endpoint."""
    print("\nTesting document validation endpoint...")
    try:
        # Create test image
        image_data = create_test_image()
        
        # Make request
        files = {'file': ('test_transport_pass.jpg', image_data, 'image/jpeg')}
        response = requests.post('http://localhost:8000/validate-document', files=files)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False


def test_extract_data():
    """Test the data extraction endpoint."""
    print("\nTesting data extraction endpoint...")
    try:
        # Create test image
        image_data = create_test_image()
        
        # Make request
        files = {'file': ('test_transport_pass.jpg', image_data, 'image/jpeg')}
        response = requests.post('http://localhost:8000/extract-data', files=files)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False


def test_invalid_file():
    """Test with an invalid file type."""
    print("\nTesting with invalid file type...")
    try:
        # Create a text file instead of image
        text_data = b"This is not an image file"
        
        files = {'file': ('test.txt', text_data, 'text/plain')}
        response = requests.post('http://localhost:8000/validate-document', files=files)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 400  # Should return error
    except Exception as e:
        print(f"Error: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Testing Transport Pass OCR API")
    print("=" * 40)
    
    tests = [
        ("Health Check", test_health_endpoint),
        ("Document Validation", test_validate_document),
        ("Data Extraction", test_extract_data),
        ("Invalid File Type", test_invalid_file),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
        print(f"✅ PASSED" if success else "❌ FAILED")
    
    print("\n" + "=" * 40)
    print("📊 Test Summary:")
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The API is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")


if __name__ == "__main__":
    main()
