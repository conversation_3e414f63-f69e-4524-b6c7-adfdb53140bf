"""
Data extraction service for Transport Pass documents.
Extracts structured data from validated Transport Pass documents.
"""

import re
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from fuzzywuzzy import fuzz
from loguru import logger

from app.models.responses import ExtractionResponse, IndentDetail
from app.services.ocr_engine import OCREngine
from app.config import get_settings


class DataExtractor:
    """Service for extracting structured data from Transport Pass documents."""
    
    def __init__(self):
        """Initialize the data extractor."""
        self.settings = get_settings()
        self.ocr_engine = OCREngine()
        
        # Field mapping patterns for single value extraction
        self.field_patterns = {
            'transportPassNo': [
                r'Transport\s+Pass\s+No[:\s]+([A-Z0-9\-]+)',
                r'Pass\s+No[:\s]+([A-Z0-9\-]+)',
                r'WHOLESALE\d+-FL\d+[A-Z]*-\d+'
            ],
            'indentNo': [
                r'Indent\s+No[:\s]+([A-Z0-9\-]+)',
                r'IND[:\s]*([A-Z0-9\-]+)'
            ],
            'gatePassDate': [
                r'Issue\s+Date[:\s]+(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
                r'Date[:\s]+(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
                r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})'
            ],
            'unitDetailsUnitName': [
                r'Unit\s+Details[:\s\n]+.*?Unit\s+Name[:\s]+([^\n]+)',
                r'Unit\s+Name[:\s]+([^\n]+)'
            ],
            'unitDetailsLicenseType': [
                r'License\s+Type[:\s]+([A-Z0-9]+)',
                r'(FL\d+[A-Z]*)',
                r'License[:\s]+([A-Z0-9]+)'
            ],
            'consigneeDetailsUnitName': [
                r'Consignee\s+Details[:\s\n]+.*?Unit\s+Name[:\s]+([^\n]+)',
                r'Consignee[:\s\n]+.*?([^\n]+)'
            ],
            'routeDetails': [
                r'Route\s+Details[:\s]+([^\n]+)',
                r'Route[:\s]+([^\n]+)'
            ],
            'majorRoute': [
                r'Major\s+Route[:\s]+([^\n]+)',
                r'Major[:\s]+([^\n]+)'
            ],
            'distanceKms': [
                r'Distance\s*\(Kms\)[:\s]+(\d+)',
                r'Distance[:\s]+(\d+)',
                r'(\d+)\s*Kms'
            ],
            'totalRequestedCases': [
                r'Total\s+No\s+of\s+Cases\s+Requested[:\s]+(\d+)',
                r'Cases\s+Requested[:\s]+(\d+)',
                r'Total.*?Cases.*?(\d+)'
            ],
            'totalRequestedBottles': [
                r'Total\s+No\.?\s+of\s+Bottles\s+Requested[:\s]+(\d+)',
                r'Bottles\s+Requested[:\s]+(\d+)',
                r'Total.*?Bottles.*?(\d+)'
            ],
            'totalDispatchedCases': [
                r'Total\s+No\s+of\s+Cases\s+Dispatched[:\s]+(\d+)',
                r'Cases\s+Dispatched[:\s]+(\d+)',
                r'Dispatched.*?Cases.*?(\d+)'
            ],
            'totalDispatchedBottles': [
                r'Total\s+No\.?\s+of\s+Bottles\s+Dispatched[:\s]+(\d+)',
                r'Bottles\s+Dispatched[:\s]+(\d+)',
                r'Dispatched.*?Bottles.*?(\d+)'
            ]
        }
        
        # Table headers for indent details
        self.table_headers = [
            'S.No', 'Serial', 'Sr',
            'Brand', 'Brand Name',
            'Description', 'Desc',
            'Packaging Size', 'Size',
            'Packaging Type', 'Type',
            'No of Cases', 'Cases', 'Mono cartons Requested', 'Requested',
            'Dispatched', 'Mono cartons Dispatched'
        ]
    
    def extract_single_field(self, text: str, field_name: str) -> Optional[str]:
        """
        Extract a single field value from text using regex patterns.
        
        Args:
            text: Full text content
            field_name: Name of the field to extract
            
        Returns:
            Extracted field value or None
        """
        if field_name not in self.field_patterns:
            return None
        
        patterns = self.field_patterns[field_name]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                # Return the first match, cleaned
                value = matches[0].strip()
                if value:
                    logger.debug(f"Extracted {field_name}: {value}")
                    return value
        
        return None
    
    def find_table_region(self, text_regions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Find and extract table region from text regions.
        
        Args:
            text_regions: List of text regions with positions
            
        Returns:
            List of text regions that belong to the table
        """
        table_regions = []
        
        # Look for table headers first
        header_y_positions = []
        
        for region in text_regions:
            text = region['text'].lower()
            for header in self.table_headers:
                if fuzz.partial_ratio(header.lower(), text) > 80:
                    header_y_positions.append(region['bbox']['y'])
                    break
        
        if not header_y_positions:
            logger.warning("No table headers found")
            return []
        
        # Find the most common Y position for headers (table start)
        table_start_y = min(header_y_positions)
        
        # Extract regions that are likely part of the table
        for region in text_regions:
            y_pos = region['bbox']['y']
            
            # Include regions that are below the table headers
            if y_pos >= table_start_y:
                # Check if the text looks like table data
                text = region['text']
                if (text.isdigit() or 
                    any(char.isdigit() for char in text) or
                    len(text.split()) <= 3):  # Short text likely to be table data
                    table_regions.append(region)
        
        logger.info(f"Found {len(table_regions)} table regions")
        return table_regions
    
    def parse_table_data(self, table_regions: List[Dict[str, Any]]) -> List[IndentDetail]:
        """
        Parse table data from text regions.
        
        Args:
            table_regions: List of text regions belonging to the table
            
        Returns:
            List of IndentDetail objects
        """
        if not table_regions:
            return []
        
        # Group regions by rows (similar Y coordinates)
        rows = {}
        tolerance = 10  # Y coordinate tolerance for grouping
        
        for region in table_regions:
            y_pos = region['bbox']['y']
            
            # Find existing row or create new one
            row_key = None
            for existing_y in rows.keys():
                if abs(y_pos - existing_y) <= tolerance:
                    row_key = existing_y
                    break
            
            if row_key is None:
                row_key = y_pos
                rows[row_key] = []
            
            rows[row_key].append(region)
        
        # Sort rows by Y position and sort columns by X position
        sorted_rows = []
        for y_pos in sorted(rows.keys()):
            row_regions = sorted(rows[y_pos], key=lambda r: r['bbox']['x'])
            row_texts = [r['text'] for r in row_regions]
            sorted_rows.append(row_texts)
        
        # Convert rows to IndentDetail objects
        indent_details = []
        
        for i, row in enumerate(sorted_rows):
            if i == 0:  # Skip header row
                continue
            
            # Map row data to IndentDetail fields
            detail = IndentDetail()
            
            if len(row) > 0:
                detail.sNo = row[0] if row[0].strip() else None
            if len(row) > 1:
                detail.brandName = row[1] if row[1].strip() else None
            if len(row) > 2:
                detail.description = row[2] if row[2].strip() else None
            if len(row) > 3:
                detail.packagingSize = row[3] if row[3].strip() else None
            if len(row) > 4:
                detail.packagingType = row[4] if row[4].strip() else None
            if len(row) > 5:
                detail.qtyRequested = row[5] if row[5].strip() else None
            if len(row) > 6:
                detail.qtyDispatched = row[6] if row[6].strip() else None
            
            # Only add if we have some meaningful data
            if any([detail.sNo, detail.brandName, detail.description]):
                indent_details.append(detail)
        
        logger.info(f"Parsed {len(indent_details)} table rows")
        return indent_details

    def extract_data(self, image: np.ndarray) -> ExtractionResponse:
        """
        Main method to extract structured data from Transport Pass document.

        Args:
            image: Preprocessed OpenCV image array

        Returns:
            ExtractionResponse with extracted data
        """
        try:
            logger.info("Starting data extraction")

            # Extract text using OCR
            ocr_result = self.ocr_engine.extract_structured_text(image)
            full_text = ocr_result['full_text']

            if not full_text:
                logger.warning("No text extracted from image")
                return ExtractionResponse()

            # Extract single value fields
            extracted_data = {}

            for field_name in self.field_patterns.keys():
                value = self.extract_single_field(full_text, field_name)
                extracted_data[field_name] = value

            # Extract text regions for table processing
            text_regions = self.ocr_engine.extract_text_regions(image)

            # Find and parse table data
            table_regions = self.find_table_region(text_regions)
            indent_details = self.parse_table_data(table_regions)

            # Create response object
            response = ExtractionResponse(
                transportPassNo=extracted_data.get('transportPassNo'),
                indentNo=extracted_data.get('indentNo'),
                gatePassDate=extracted_data.get('gatePassDate'),
                unitDetailsUnitName=extracted_data.get('unitDetailsUnitName'),
                unitDetailsLicenseType=extracted_data.get('unitDetailsLicenseType'),
                consigneeDetailsUnitName=extracted_data.get('consigneeDetailsUnitName'),
                routeDetails=extracted_data.get('routeDetails'),
                majorRoute=extracted_data.get('majorRoute'),
                distanceKms=extracted_data.get('distanceKms'),
                totalRequestedCases=extracted_data.get('totalRequestedCases'),
                totalRequestedBottles=extracted_data.get('totalRequestedBottles'),
                totalDispatchedCases=extracted_data.get('totalDispatchedCases'),
                totalDispatchedBottles=extracted_data.get('totalDispatchedBottles'),
                indentDetails=indent_details
            )

            # Log extraction summary
            extracted_fields = sum(1 for v in extracted_data.values() if v is not None)
            logger.info(f"Data extraction completed. Extracted {extracted_fields}/{len(self.field_patterns)} fields and {len(indent_details)} table rows")

            return response

        except Exception as e:
            logger.error(f"Error extracting data: {str(e)}")
            return ExtractionResponse()

    def validate_extracted_data(self, response: ExtractionResponse) -> Dict[str, Any]:
        """
        Validate the quality of extracted data.

        Args:
            response: ExtractionResponse object

        Returns:
            Dictionary with validation results
        """
        validation_results = {
            'total_fields': 13,  # Total number of single-value fields
            'extracted_fields': 0,
            'missing_fields': [],
            'table_rows': len(response.indentDetails),
            'quality_score': 0.0
        }

        # Check single-value fields
        field_values = [
            response.transportPassNo,
            response.indentNo,
            response.gatePassDate,
            response.unitDetailsUnitName,
            response.unitDetailsLicenseType,
            response.consigneeDetailsUnitName,
            response.routeDetails,
            response.majorRoute,
            response.distanceKms,
            response.totalRequestedCases,
            response.totalRequestedBottles,
            response.totalDispatchedCases,
            response.totalDispatchedBottles
        ]

        field_names = [
            'transportPassNo', 'indentNo', 'gatePassDate',
            'unitDetailsUnitName', 'unitDetailsLicenseType', 'consigneeDetailsUnitName',
            'routeDetails', 'majorRoute', 'distanceKms',
            'totalRequestedCases', 'totalRequestedBottles',
            'totalDispatchedCases', 'totalDispatchedBottles'
        ]

        for i, value in enumerate(field_values):
            if value is not None and value.strip():
                validation_results['extracted_fields'] += 1
            else:
                validation_results['missing_fields'].append(field_names[i])

        # Calculate quality score
        field_score = validation_results['extracted_fields'] / validation_results['total_fields']
        table_score = min(len(response.indentDetails) / 5, 1.0)  # Assume 5 rows is good

        validation_results['quality_score'] = (field_score * 0.8) + (table_score * 0.2)

        logger.info(f"Data validation: {validation_results['extracted_fields']}/{validation_results['total_fields']} fields, quality score: {validation_results['quality_score']:.2f}")

        return validation_results
