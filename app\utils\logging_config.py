"""
Logging configuration for the Transport Pass OCR API.
"""

import sys
import os
from pathlib import Path
from loguru import logger
from typing import Optional

from app.config import get_settings


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    enable_console: bool = True,
    enable_file: bool = True
) -> None:
    """
    Setup comprehensive logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Custom log file path
        enable_console: Enable console logging
        enable_file: Enable file logging
    """
    settings = get_settings()
    
    # Remove default handler
    logger.remove()
    
    # Console logging
    if enable_console:
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            level=log_level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
    
    # File logging
    if enable_file:
        # Ensure logs directory exists
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Main application log
        main_log_file = log_file or log_dir / "app.log"
        logger.add(
            main_log_file,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | "
                   "{level: <8} | "
                   "{name}:{function}:{line} | "
                   "{message}",
            level=log_level,
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            backtrace=True,
            diagnose=True
        )
        
        # Error-only log
        error_log_file = log_dir / "errors.log"
        logger.add(
            error_log_file,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | "
                   "{level: <8} | "
                   "{name}:{function}:{line} | "
                   "{message} | "
                   "{extra}",
            level="ERROR",
            rotation="5 MB",
            retention="60 days",
            compression="zip",
            backtrace=True,
            diagnose=True
        )
        
        # OCR processing log
        ocr_log_file = log_dir / "ocr.log"
        logger.add(
            ocr_log_file,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | "
                   "{level: <8} | "
                   "{message}",
            level="INFO",
            rotation="20 MB",
            retention="14 days",
            filter=lambda record: "ocr" in record["name"].lower() or "OCR" in record["message"],
            compression="zip"
        )
        
        # API access log
        api_log_file = log_dir / "api_access.log"
        logger.add(
            api_log_file,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | "
                   "{level: <8} | "
                   "{message}",
            level="INFO",
            rotation="50 MB",
            retention="30 days",
            filter=lambda record: "endpoint" in record["extra"] if record.get("extra") else False,
            compression="zip"
        )
    
    logger.info(f"Logging configured with level: {log_level}")


def log_api_access(
    method: str,
    path: str,
    status_code: int,
    response_time: float,
    client_ip: str = "unknown",
    user_agent: str = "unknown",
    file_size: Optional[int] = None
) -> None:
    """
    Log API access information.
    
    Args:
        method: HTTP method
        path: Request path
        status_code: Response status code
        response_time: Response time in seconds
        client_ip: Client IP address
        user_agent: User agent string
        file_size: Uploaded file size in bytes
    """
    extra_info = {
        "endpoint": path,
        "method": method,
        "status_code": status_code,
        "response_time": response_time,
        "client_ip": client_ip,
        "user_agent": user_agent
    }
    
    if file_size is not None:
        extra_info["file_size"] = file_size
    
    logger.bind(**extra_info).info(
        f"{method} {path} - {status_code} - {response_time:.3f}s"
    )


def log_ocr_processing(
    filename: str,
    processing_time: float,
    confidence: float,
    extracted_fields: int,
    total_fields: int,
    engine_used: str = "unknown"
) -> None:
    """
    Log OCR processing information.
    
    Args:
        filename: Processed filename
        processing_time: Processing time in seconds
        confidence: OCR confidence score
        extracted_fields: Number of extracted fields
        total_fields: Total number of expected fields
        engine_used: OCR engine used
    """
    logger.bind(
        ocr_processing=True,
        filename=filename,
        processing_time=processing_time,
        confidence=confidence,
        extracted_fields=extracted_fields,
        total_fields=total_fields,
        engine_used=engine_used
    ).info(
        f"OCR processing completed for {filename} - "
        f"Engine: {engine_used}, "
        f"Confidence: {confidence:.2f}, "
        f"Fields: {extracted_fields}/{total_fields}, "
        f"Time: {processing_time:.3f}s"
    )


def log_validation_result(
    filename: str,
    is_valid: bool,
    document_type: str,
    confidence: float,
    detected_elements: list,
    processing_time: float
) -> None:
    """
    Log document validation results.
    
    Args:
        filename: Validated filename
        is_valid: Validation result
        document_type: Detected document type
        confidence: Validation confidence
        detected_elements: List of detected elements
        processing_time: Processing time in seconds
    """
    logger.bind(
        validation=True,
        filename=filename,
        is_valid=is_valid,
        document_type=document_type,
        confidence=confidence,
        detected_elements=detected_elements,
        processing_time=processing_time
    ).info(
        f"Document validation for {filename} - "
        f"Valid: {is_valid}, "
        f"Type: {document_type}, "
        f"Confidence: {confidence:.2f}, "
        f"Elements: {len(detected_elements)}, "
        f"Time: {processing_time:.3f}s"
    )


def log_error_with_context(
    error: Exception,
    context: str,
    filename: Optional[str] = None,
    additional_info: Optional[dict] = None
) -> None:
    """
    Log error with additional context.
    
    Args:
        error: Exception object
        context: Context where error occurred
        filename: Related filename if applicable
        additional_info: Additional information dictionary
    """
    error_info = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "context": context
    }
    
    if filename:
        error_info["filename"] = filename
    
    if additional_info:
        error_info.update(additional_info)
    
    logger.bind(**error_info).error(
        f"Error in {context}: {str(error)}"
    )


def get_logger(name: str):
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logger.bind(logger_name=name)
