"""
Test cases for the image processor utility.
"""

import pytest
import numpy as np
import cv2
from PIL import Image
import io

from app.utils.image_processor import ImageProcessor


class TestImageProcessor:
    """Test cases for ImageProcessor class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.processor = ImageProcessor()
    
    def create_test_image(self, width=800, height=600):
        """Create a test image."""
        # Create a simple test image
        image = Image.new('RGB', (width, height), color='white')
        
        # Convert to bytes
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='JPEG')
        return img_byte_arr.getvalue()
    
    def test_load_image_from_bytes(self):
        """Test loading image from bytes."""
        image_bytes = self.create_test_image()
        
        result = self.processor.load_image_from_bytes(image_bytes)
        
        assert isinstance(result, np.ndarray)
        assert len(result.shape) == 3  # Height, Width, Channels
        assert result.shape[2] == 3  # BGR channels
    
    def test_load_image_invalid_bytes(self):
        """Test loading image from invalid bytes."""
        invalid_bytes = b"This is not an image"
        
        with pytest.raises(ValueError):
            self.processor.load_image_from_bytes(invalid_bytes)
    
    def test_detect_rotation_angle(self):
        """Test rotation angle detection."""
        # Create a simple test image
        image = np.ones((600, 800, 3), dtype=np.uint8) * 255
        
        # Add some horizontal lines for rotation detection
        cv2.line(image, (100, 200), (700, 200), (0, 0, 0), 2)
        cv2.line(image, (100, 300), (700, 300), (0, 0, 0), 2)
        cv2.line(image, (100, 400), (700, 400), (0, 0, 0), 2)
        
        angle = self.processor.detect_rotation_angle(image)
        
        # Should detect approximately 0 degrees for horizontal lines
        assert isinstance(angle, float)
        assert -5 <= angle <= 5  # Allow some tolerance
    
    def test_rotate_image(self):
        """Test image rotation."""
        # Create a test image
        image = np.ones((600, 800, 3), dtype=np.uint8) * 255
        
        # Rotate by 10 degrees
        rotated = self.processor.rotate_image(image, 10)
        
        assert isinstance(rotated, np.ndarray)
        assert len(rotated.shape) == 3
        # Rotated image might have different dimensions
        assert rotated.shape[2] == 3
    
    def test_rotate_image_small_angle(self):
        """Test rotation with very small angle (should skip rotation)."""
        image = np.ones((600, 800, 3), dtype=np.uint8) * 255
        
        # Very small angle should return original image
        rotated = self.processor.rotate_image(image, 0.1)
        
        np.testing.assert_array_equal(image, rotated)
    
    def test_enhance_image_quality(self):
        """Test image quality enhancement."""
        image = np.ones((600, 800, 3), dtype=np.uint8) * 128  # Gray image
        
        enhanced = self.processor.enhance_image_quality(image)
        
        assert isinstance(enhanced, np.ndarray)
        assert enhanced.shape == image.shape
    
    def test_denoise_image(self):
        """Test image denoising."""
        # Create a noisy image
        image = np.random.randint(0, 255, (600, 800, 3), dtype=np.uint8)
        
        denoised = self.processor.denoise_image(image)
        
        assert isinstance(denoised, np.ndarray)
        assert denoised.shape == image.shape
    
    def test_correct_skew(self):
        """Test skew correction."""
        image = np.ones((600, 800, 3), dtype=np.uint8) * 255
        
        corrected = self.processor.correct_skew(image)
        
        assert isinstance(corrected, np.ndarray)
        assert len(corrected.shape) == 3
    
    def test_resize_image(self):
        """Test image resizing."""
        # Create a large image
        image = np.ones((2000, 3000, 3), dtype=np.uint8) * 255
        
        resized = self.processor.resize_image(image, max_width=1000, max_height=1000)
        
        assert isinstance(resized, np.ndarray)
        assert resized.shape[0] <= 1000  # Height
        assert resized.shape[1] <= 1000  # Width
    
    def test_resize_image_small(self):
        """Test resizing small image (should not upscale)."""
        image = np.ones((300, 400, 3), dtype=np.uint8) * 255
        
        resized = self.processor.resize_image(image, max_width=1000, max_height=1000)
        
        # Should return original image (no upscaling)
        np.testing.assert_array_equal(image, resized)
    
    def test_preprocess_for_ocr(self):
        """Test complete preprocessing pipeline."""
        image_bytes = self.create_test_image()
        
        processed = self.processor.preprocess_for_ocr(image_bytes)
        
        assert isinstance(processed, np.ndarray)
        assert len(processed.shape) == 3
        assert processed.shape[2] == 3
    
    def test_convert_to_grayscale(self):
        """Test grayscale conversion."""
        # Color image
        color_image = np.ones((600, 800, 3), dtype=np.uint8) * 128
        
        gray = self.processor.convert_to_grayscale(color_image)
        
        assert isinstance(gray, np.ndarray)
        assert len(gray.shape) == 2  # Grayscale has 2 dimensions
    
    def test_convert_to_grayscale_already_gray(self):
        """Test grayscale conversion on already gray image."""
        gray_image = np.ones((600, 800), dtype=np.uint8) * 128
        
        result = self.processor.convert_to_grayscale(gray_image)
        
        np.testing.assert_array_equal(gray_image, result)


if __name__ == "__main__":
    pytest.main([__file__])
