"""
Usage example for the Transport Pass OCR API.

This script demonstrates how to use the API endpoints for document validation
and data extraction.
"""

import requests
import json
import os
from pathlib import Path
import time


class TransportPassOCRClient:
    """Client for interacting with the Transport Pass OCR API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        Initialize the client.
        
        Args:
            base_url: Base URL of the API service
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def health_check(self) -> dict:
        """
        Check the health status of the API.
        
        Returns:
            Health status response
        """
        try:
            response = self.session.get(f"{self.base_url}/health")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": str(e)}
    
    def validate_document(self, file_path: str) -> dict:
        """
        Validate a Transport Pass document.
        
        Args:
            file_path: Path to the image file
            
        Returns:
            Validation response
        """
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f, 'image/jpeg')}
                response = self.session.post(
                    f"{self.base_url}/validate-document",
                    files=files
                )
                response.raise_for_status()
                return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": str(e)}
        except FileNotFoundError:
            return {"error": f"File not found: {file_path}"}
    
    def extract_data(self, file_path: str) -> dict:
        """
        Extract data from a Transport Pass document.
        
        Args:
            file_path: Path to the image file
            
        Returns:
            Extraction response
        """
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f, 'image/jpeg')}
                response = self.session.post(
                    f"{self.base_url}/extract-data",
                    files=files
                )
                response.raise_for_status()
                return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": str(e)}
        except FileNotFoundError:
            return {"error": f"File not found: {file_path}"}


def print_json(data: dict, title: str = ""):
    """Pretty print JSON data."""
    if title:
        print(f"\n{title}")
        print("=" * len(title))
    print(json.dumps(data, indent=2, ensure_ascii=False))


def main():
    """Main example function."""
    print("Transport Pass OCR API - Usage Example")
    print("=" * 40)
    
    # Initialize client
    client = TransportPassOCRClient()
    
    # 1. Health Check
    print("\n1. Checking API Health...")
    health = client.health_check()
    print_json(health, "Health Check Response")
    
    if "error" in health:
        print("\n❌ API is not available. Please ensure the server is running.")
        return
    
    print("\n✅ API is healthy and running!")
    
    # 2. Document Validation Example
    print("\n2. Document Validation Example")
    print("-" * 30)
    
    # You would replace this with an actual image file path
    sample_image_path = "sample_transport_pass.jpg"
    
    if not os.path.exists(sample_image_path):
        print(f"⚠️  Sample image not found: {sample_image_path}")
        print("Please provide a valid Transport Pass image file.")
        
        # Create a simple example for demonstration
        print("\nExample validation response:")
        example_validation = {
            "isValid": True,
            "documentType": "transport_pass",
            "confidence": 0.85,
            "reason": "Document contains required elements for Transport Pass",
            "detectedElements": [
                "DEPARTMENT OF EXCISE",
                "UTTAR PRADESH",
                "QR Code/Barcode",
                "Government Seal",
                "WHOLESALE10601-FL2B-15232"
            ]
        }
        print_json(example_validation)
    else:
        print(f"Validating document: {sample_image_path}")
        start_time = time.time()
        
        validation_result = client.validate_document(sample_image_path)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print_json(validation_result, "Validation Result")
        print(f"\nProcessing time: {processing_time:.2f} seconds")
        
        if "error" in validation_result:
            print("❌ Validation failed!")
            return
        
        if validation_result.get("isValid"):
            print("✅ Document is a valid Transport Pass!")
        else:
            print("❌ Document is not a valid Transport Pass.")
            print(f"Reason: {validation_result.get('reason', 'Unknown')}")
    
    # 3. Data Extraction Example
    print("\n3. Data Extraction Example")
    print("-" * 25)
    
    if not os.path.exists(sample_image_path):
        print("Example extraction response:")
        example_extraction = {
            "transportPassNo": "WHOLESALE10601-FL2B-15232",
            "indentNo": "IND123456",
            "gatePassDate": "15/01/2024",
            "unitDetailsUnitName": "Sample Unit Name",
            "unitDetailsLicenseType": "FL2B",
            "consigneeDetailsUnitName": "Sample Consignee",
            "routeDetails": "Sample Route Details",
            "majorRoute": "Major Route Sample",
            "distanceKms": "150",
            "totalRequestedCases": "100",
            "totalRequestedBottles": "2400",
            "totalDispatchedCases": "100",
            "totalDispatchedBottles": "2400",
            "indentDetails": [
                {
                    "sNo": "1",
                    "brandName": "Brand A",
                    "description": "Premium Liquor",
                    "packagingSize": "750ml",
                    "packagingType": "Bottle",
                    "qtyRequested": "50",
                    "qtyDispatched": "50"
                },
                {
                    "sNo": "2",
                    "brandName": "Brand B",
                    "description": "Standard Liquor",
                    "packagingSize": "750ml",
                    "packagingType": "Bottle",
                    "qtyRequested": "50",
                    "qtyDispatched": "50"
                }
            ]
        }
        print_json(example_extraction)
    else:
        print(f"Extracting data from: {sample_image_path}")
        start_time = time.time()
        
        extraction_result = client.extract_data(sample_image_path)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print_json(extraction_result, "Extraction Result")
        print(f"\nProcessing time: {processing_time:.2f} seconds")
        
        if "error" in extraction_result:
            print("❌ Data extraction failed!")
            return
        
        # Analyze extraction results
        extracted_fields = sum(1 for v in extraction_result.values() 
                             if v is not None and v != "" and not isinstance(v, list))
        table_rows = len(extraction_result.get("indentDetails", []))
        
        print(f"\n📊 Extraction Summary:")
        print(f"   - Extracted fields: {extracted_fields}")
        print(f"   - Table rows: {table_rows}")
        print("✅ Data extraction completed!")
    
    # 4. Error Handling Example
    print("\n4. Error Handling Example")
    print("-" * 25)
    
    print("Testing with invalid file...")
    invalid_result = client.validate_document("non_existent_file.jpg")
    print_json(invalid_result, "Error Response")
    
    print("\n🎉 Example completed successfully!")
    print("\nNext steps:")
    print("1. Replace 'sample_transport_pass.jpg' with your actual image file")
    print("2. Test with different types of documents")
    print("3. Integrate the client into your application")
    print("4. Check the API documentation at http://localhost:8000/docs")


if __name__ == "__main__":
    main()
