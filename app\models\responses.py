"""
Response models for the Transport Pass OCR API.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Union
from datetime import datetime


class ValidationResponse(BaseModel):
    """Response model for document validation."""
    isValid: bool = Field(..., description="Whether the document is a valid Transport Pass")
    documentType: str = Field(..., description="Type of document detected")
    confidence: float = Field(..., ge=0, le=1, description="Confidence score for classification")
    reason: str = Field(..., description="Brief explanation of classification")
    detectedElements: List[str] = Field(default_factory=list, description="List of key elements found")


class IndentDetail(BaseModel):
    """Model for individual indent detail row."""
    sNo: Optional[str] = Field(None, description="Serial Number")
    brandName: Optional[str] = Field(None, description="Brand Name")
    description: Optional[str] = Field(None, description="Product Description")
    packagingSize: Optional[str] = Field(None, description="Packaging Size")
    packagingType: Optional[str] = Field(None, description="Packaging Type")
    qtyRequested: Optional[str] = Field(None, description="Quantity Requested")
    qtyDispatched: Optional[str] = Field(None, description="Quantity Dispatched")


class ExtractionResponse(BaseModel):
    """Response model for data extraction."""
    # Single Value Fields
    transportPassNo: Optional[str] = Field(None, description="Transport Pass Number")
    indentNo: Optional[str] = Field(None, description="Indent Number")
    gatePassDate: Optional[str] = Field(None, description="Gate Pass Issue Date")
    unitDetailsUnitName: Optional[str] = Field(None, description="Unit Details - Unit Name")
    unitDetailsLicenseType: Optional[str] = Field(None, description="Unit Details - License Type")
    consigneeDetailsUnitName: Optional[str] = Field(None, description="Consignee Details - Unit Name")
    routeDetails: Optional[str] = Field(None, description="Route Details")
    majorRoute: Optional[str] = Field(None, description="Major Route")
    distanceKms: Optional[str] = Field(None, description="Distance in Kilometers")
    totalRequestedCases: Optional[str] = Field(None, description="Total Requested Cases")
    totalRequestedBottles: Optional[str] = Field(None, description="Total Requested Bottles")
    totalDispatchedCases: Optional[str] = Field(None, description="Total Dispatched Cases")
    totalDispatchedBottles: Optional[str] = Field(None, description="Total Dispatched Bottles")
    
    # Table Data
    indentDetails: List[IndentDetail] = Field(default_factory=list, description="Indent Details Table")


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[str] = Field(None, description="Additional error details")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="Error timestamp")
