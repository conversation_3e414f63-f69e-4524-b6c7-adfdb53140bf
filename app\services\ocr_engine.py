"""
OCR engine service for text extraction from Transport Pass documents.
Integrates multiple OCR engines for optimal text recognition.
"""

import cv2
import numpy as np
import pytesseract
import easyocr
from typing import List, Dict, Any, Optional, Tuple
import re
from loguru import logger
import os

from app.config import get_settings


class OCREngine:
    """Service for OCR text extraction using multiple engines."""
    
    def __init__(self):
        """Initialize OCR engines."""
        self.settings = get_settings()
        
        # Configure Tesseract
        if self.settings.tesseract_cmd_path:
            pytesseract.pytesseract.tesseract_cmd = self.settings.tesseract_cmd_path
        
        # Initialize EasyOCR
        try:
            self.easyocr_reader = easyocr.Reader(['en'], gpu=False)
            logger.info("EasyOCR initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize EasyOCR: {str(e)}")
            self.easyocr_reader = None
        
        # Tesseract configuration for better accuracy
        self.tesseract_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,:-/() '
    
    def extract_text_tesseract(self, image: np.ndarray) -> Dict[str, Any]:
        """
        Extract text using Tesseract OCR.
        
        Args:
            image: OpenCV image array
            
        Returns:
            Dictionary with extracted text and confidence scores
        """
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            # Extract text with confidence scores
            data = pytesseract.image_to_data(
                gray, 
                config=self.tesseract_config,
                output_type=pytesseract.Output.DICT
            )
            
            # Filter out low confidence text
            texts = []
            confidences = []
            
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                conf = int(data['conf'][i])
                
                if text and conf > self.settings.ocr_confidence_threshold * 100:
                    texts.append(text)
                    confidences.append(conf / 100.0)
            
            # Combine all text
            full_text = ' '.join(texts)
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
            
            logger.info(f"Tesseract extracted {len(texts)} text elements with avg confidence {avg_confidence:.2f}")
            
            return {
                'engine': 'tesseract',
                'text': full_text,
                'texts': texts,
                'confidence': avg_confidence,
                'word_confidences': confidences
            }
            
        except Exception as e:
            logger.error(f"Error with Tesseract OCR: {str(e)}")
            return {
                'engine': 'tesseract',
                'text': '',
                'texts': [],
                'confidence': 0.0,
                'word_confidences': []
            }
    
    def extract_text_easyocr(self, image: np.ndarray) -> Dict[str, Any]:
        """
        Extract text using EasyOCR.
        
        Args:
            image: OpenCV image array
            
        Returns:
            Dictionary with extracted text and confidence scores
        """
        try:
            if self.easyocr_reader is None:
                logger.warning("EasyOCR not available")
                return {
                    'engine': 'easyocr',
                    'text': '',
                    'texts': [],
                    'confidence': 0.0,
                    'word_confidences': []
                }
            
            # Extract text
            results = self.easyocr_reader.readtext(image)
            
            # Filter results by confidence
            texts = []
            confidences = []
            
            for (bbox, text, conf) in results:
                if conf > self.settings.ocr_confidence_threshold:
                    texts.append(text.strip())
                    confidences.append(conf)
            
            # Combine all text
            full_text = ' '.join(texts)
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
            
            logger.info(f"EasyOCR extracted {len(texts)} text elements with avg confidence {avg_confidence:.2f}")
            
            return {
                'engine': 'easyocr',
                'text': full_text,
                'texts': texts,
                'confidence': avg_confidence,
                'word_confidences': confidences
            }
            
        except Exception as e:
            logger.error(f"Error with EasyOCR: {str(e)}")
            return {
                'engine': 'easyocr',
                'text': '',
                'texts': [],
                'confidence': 0.0,
                'word_confidences': []
            }
    
    def extract_text_combined(self, image: np.ndarray) -> Dict[str, Any]:
        """
        Extract text using multiple OCR engines and combine results.
        
        Args:
            image: OpenCV image array
            
        Returns:
            Dictionary with combined OCR results
        """
        try:
            logger.info("Starting combined OCR extraction")
            
            # Run both OCR engines
            tesseract_result = self.extract_text_tesseract(image)
            easyocr_result = self.extract_text_easyocr(image)
            
            # Choose the best result based on confidence
            if tesseract_result['confidence'] >= easyocr_result['confidence']:
                primary_result = tesseract_result
                secondary_result = easyocr_result
            else:
                primary_result = easyocr_result
                secondary_result = tesseract_result
            
            # Combine unique texts from both engines
            all_texts = list(set(primary_result['texts'] + secondary_result['texts']))
            combined_text = ' '.join(all_texts)
            
            # Calculate weighted confidence
            total_confidence = (
                primary_result['confidence'] * 0.7 + 
                secondary_result['confidence'] * 0.3
            )
            
            result = {
                'engine': 'combined',
                'text': combined_text,
                'texts': all_texts,
                'confidence': total_confidence,
                'primary_engine': primary_result['engine'],
                'secondary_engine': secondary_result['engine'],
                'tesseract_result': tesseract_result,
                'easyocr_result': easyocr_result
            }
            
            logger.info(f"Combined OCR completed with confidence {total_confidence:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"Error in combined OCR: {str(e)}")
            return {
                'engine': 'combined',
                'text': '',
                'texts': [],
                'confidence': 0.0,
                'primary_engine': 'none',
                'secondary_engine': 'none'
            }

    def clean_text(self, text: str) -> str:
        """
        Clean and normalize extracted text.

        Args:
            text: Raw OCR text

        Returns:
            Cleaned text
        """
        if not text:
            return ""

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove special characters that might be OCR artifacts
        text = re.sub(r'[^\w\s.,:-/()]', ' ', text)

        # Fix common OCR errors
        text = text.replace('|', 'I')
        text = text.replace('0', 'O', 1) if text.startswith('0') else text

        return text.strip()

    def extract_structured_text(self, image: np.ndarray) -> Dict[str, Any]:
        """
        Extract text with structure preservation for form processing.

        Args:
            image: OpenCV image array

        Returns:
            Dictionary with structured text extraction results
        """
        try:
            # Use combined OCR for best results
            ocr_result = self.extract_text_combined(image)

            # Clean the extracted text
            cleaned_texts = [self.clean_text(text) for text in ocr_result['texts']]
            cleaned_full_text = self.clean_text(ocr_result['text'])

            # Extract lines and structure
            lines = [line.strip() for line in cleaned_full_text.split('\n') if line.strip()]

            # Group related text elements
            structured_data = {
                'full_text': cleaned_full_text,
                'lines': lines,
                'cleaned_texts': cleaned_texts,
                'confidence': ocr_result['confidence'],
                'engine_used': ocr_result.get('primary_engine', 'combined')
            }

            logger.info(f"Structured text extraction completed with {len(lines)} lines")
            return structured_data

        except Exception as e:
            logger.error(f"Error in structured text extraction: {str(e)}")
            return {
                'full_text': '',
                'lines': [],
                'cleaned_texts': [],
                'confidence': 0.0,
                'engine_used': 'none'
            }

    def extract_text_regions(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        Extract text from specific regions of the image.

        Args:
            image: OpenCV image array

        Returns:
            List of text regions with their content and positions
        """
        try:
            # Convert to grayscale
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            # Use Tesseract to get word-level data with positions
            data = pytesseract.image_to_data(
                gray,
                config=self.tesseract_config,
                output_type=pytesseract.Output.DICT
            )

            regions = []

            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                conf = int(data['conf'][i])

                if text and conf > self.settings.ocr_confidence_threshold * 100:
                    region = {
                        'text': self.clean_text(text),
                        'confidence': conf / 100.0,
                        'bbox': {
                            'x': data['left'][i],
                            'y': data['top'][i],
                            'width': data['width'][i],
                            'height': data['height'][i]
                        },
                        'level': data['level'][i]
                    }
                    regions.append(region)

            logger.info(f"Extracted {len(regions)} text regions")
            return regions

        except Exception as e:
            logger.error(f"Error extracting text regions: {str(e)}")
            return []
