"""
Error handling utilities for the Transport Pass OCR API.
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from loguru import logger
from typing import Dict, Any, Optional
import traceback
from datetime import datetime

from app.models.responses import ErrorResponse
from app.config import get_settings


class APIError(Exception):
    """Custom API error class."""
    
    def __init__(
        self, 
        message: str, 
        status_code: int = 500, 
        error_type: str = "APIError",
        details: Optional[str] = None
    ):
        self.message = message
        self.status_code = status_code
        self.error_type = error_type
        self.details = details
        super().__init__(self.message)


class ValidationError(APIError):
    """Error for validation failures."""
    
    def __init__(self, message: str, details: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=400,
            error_type="ValidationError",
            details=details
        )


class ProcessingError(APIError):
    """Error for processing failures."""
    
    def __init__(self, message: str, details: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=422,
            error_type="ProcessingError",
            details=details
        )


class FileError(APIError):
    """Error for file-related issues."""
    
    def __init__(self, message: str, details: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=400,
            error_type="FileError",
            details=details
        )


class OCRError(APIError):
    """Error for OCR processing issues."""
    
    def __init__(self, message: str, details: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=422,
            error_type="OCRError",
            details=details
        )


def create_error_response(
    error_type: str,
    message: str,
    details: Optional[str] = None,
    status_code: int = 500
) -> JSONResponse:
    """
    Create a standardized error response.
    
    Args:
        error_type: Type of error
        message: Error message
        details: Additional error details
        status_code: HTTP status code
        
    Returns:
        JSONResponse with error details
    """
    settings = get_settings()
    
    error_response = ErrorResponse(
        error=error_type,
        message=message,
        details=details if settings.debug else None,
        timestamp=datetime.now()
    )
    
    return JSONResponse(
        status_code=status_code,
        content=error_response.model_dump()
    )


def log_error(
    error: Exception,
    context: str = "",
    request_id: Optional[str] = None,
    user_id: Optional[str] = None
) -> None:
    """
    Log error with context information.
    
    Args:
        error: Exception object
        context: Context where error occurred
        request_id: Request identifier
        user_id: User identifier
    """
    error_info = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "context": context,
        "request_id": request_id,
        "user_id": user_id,
        "traceback": traceback.format_exc()
    }
    
    logger.error(f"Error in {context}: {error_info}")


async def api_error_handler(request: Request, exc: APIError) -> JSONResponse:
    """
    Handle custom API errors.
    
    Args:
        request: FastAPI request object
        exc: APIError exception
        
    Returns:
        JSONResponse with error details
    """
    log_error(exc, context=f"{request.method} {request.url.path}")
    
    return create_error_response(
        error_type=exc.error_type,
        message=exc.message,
        details=exc.details,
        status_code=exc.status_code
    )


async def validation_error_handler(request: Request, exc: ValidationError) -> JSONResponse:
    """
    Handle validation errors.
    
    Args:
        request: FastAPI request object
        exc: ValidationError exception
        
    Returns:
        JSONResponse with validation error details
    """
    log_error(exc, context=f"Validation error in {request.method} {request.url.path}")
    
    return create_error_response(
        error_type="ValidationError",
        message=exc.message,
        details=exc.details,
        status_code=400
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """
    Handle HTTP exceptions.
    
    Args:
        request: FastAPI request object
        exc: HTTPException
        
    Returns:
        JSONResponse with HTTP error details
    """
    log_error(exc, context=f"HTTP error in {request.method} {request.url.path}")
    
    return create_error_response(
        error_type="HTTPError",
        message=exc.detail,
        status_code=exc.status_code
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle general exceptions.
    
    Args:
        request: FastAPI request object
        exc: General exception
        
    Returns:
        JSONResponse with general error details
    """
    log_error(exc, context=f"Unhandled error in {request.method} {request.url.path}")
    
    settings = get_settings()
    
    return create_error_response(
        error_type="InternalServerError",
        message="An unexpected error occurred",
        details=str(exc) if settings.debug else None,
        status_code=500
    )


def validate_file_upload(file_content: bytes, filename: str) -> None:
    """
    Validate uploaded file.
    
    Args:
        file_content: File content bytes
        filename: Original filename
        
    Raises:
        FileError: If file validation fails
    """
    settings = get_settings()
    
    # Check file size
    if len(file_content) > settings.max_file_size:
        raise FileError(
            message=f"File too large. Maximum size: {settings.max_file_size / (1024*1024):.1f}MB",
            details=f"File size: {len(file_content) / (1024*1024):.1f}MB"
        )
    
    # Check file extension
    if not filename.lower().endswith(tuple(settings.allowed_extensions)):
        raise FileError(
            message=f"Unsupported file type",
            details=f"Allowed extensions: {', '.join(settings.allowed_extensions)}"
        )
    
    # Check if file is empty
    if len(file_content) == 0:
        raise FileError(
            message="Empty file uploaded",
            details="File contains no data"
        )


def handle_ocr_errors(func):
    """
    Decorator to handle OCR-related errors.
    
    Args:
        func: Function to wrap
        
    Returns:
        Wrapped function with error handling
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"OCR error in {func.__name__}: {str(e)}")
            raise OCRError(
                message="OCR processing failed",
                details=str(e)
            )
    return wrapper


def handle_processing_errors(func):
    """
    Decorator to handle processing-related errors.
    
    Args:
        func: Function to wrap
        
    Returns:
        Wrapped function with error handling
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Processing error in {func.__name__}: {str(e)}")
            raise ProcessingError(
                message="Document processing failed",
                details=str(e)
            )
    return wrapper
