2025-08-13 13:29:59.639 | ERROR    | app.main:validate_document:132 | Error validating document: Unsupported file type | {}
2025-08-13 13:29:59.674 | ERROR    | app.utils.error_handler:log_error:138 | Error in HTTP error in POST /validate-document: {'error_type': 'HTTPException', 'error_message': '500: Internal server error: Unsupported file type', 'context': 'HTTP error in POST /validate-document', 'request_id': None, 'user_id': None, 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\OneDrive\\Desktop\\OCR\\app\\main.py", line 107, in validate_document\n    validate_file_upload(file_content, file.filename)\n  File "C:\\Users\\<USER>\\OneDrive\\Desktop\\OCR\\app\\utils\\error_handler.py", line 248, in validate_file_upload\n    raise FileError(\napp.utils.error_handler.FileError: Unsupported file type\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\_exception_handler.py", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\routing.py", line 73, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\fastapi\\routing.py", line 301, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\fastapi\\routing.py", line 212, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\OneDrive\\Desktop\\OCR\\app\\main.py", line 133, in validate_document\n    raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")\nfastapi.exceptions.HTTPException: 500: Internal server error: Unsupported file type\n'} | {}
2025-08-13 13:29:59.827 | ERROR    | app.utils.error_handler:log_error:138 | Error in Unhandled error in POST /validate-document: {'error_type': 'TypeError', 'error_message': 'Object of type datetime is not JSON serializable', 'context': 'Unhandled error in POST /validate-document', 'request_id': None, 'user_id': None, 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\OneDrive\\Desktop\\OCR\\app\\main.py", line 107, in validate_document\n    validate_file_upload(file_content, file.filename)\n  File "C:\\Users\\<USER>\\OneDrive\\Desktop\\OCR\\app\\utils\\error_handler.py", line 248, in validate_file_upload\n    raise FileError(\napp.utils.error_handler.FileError: Unsupported file type\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\_exception_handler.py", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\routing.py", line 73, in app\n    response = await f(request)\n               ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\fastapi\\routing.py", line 301, in app\n    raw_response = await run_endpoint_function(\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\fastapi\\routing.py", line 212, in run_endpoint_function\n    return await dependant.call(**values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\OneDrive\\Desktop\\OCR\\app\\main.py", line 133, in validate_document\n    raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")\nfastapi.exceptions.HTTPException: 500: Internal server error: Unsupported file type\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\errors.py", line 165, in __call__\n    await self.app(scope, receive, _send)\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\cors.py", line 85, in __call__\n    await self.app(scope, receive, send)\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\middleware\\exceptions.py", line 62, in __call__\n    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\_exception_handler.py", line 53, in wrapped_app\n    raise exc\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\_exception_handler.py", line 42, in wrapped_app\n    await app(scope, receive, sender)\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\routing.py", line 714, in __call__\n    await self.middleware_stack(scope, receive, send)\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\routing.py", line 734, in app\n    await route.handle(scope, receive, send)\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\routing.py", line 288, in handle\n    await self.app(scope, receive, send)\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\routing.py", line 76, in app\n    await wrap_app_handling_exceptions(app, request)(scope, receive, send)\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\_exception_handler.py", line 59, in wrapped_app\n    response = await handler(conn, exc)\n               ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\OneDrive\\Desktop\\OCR\\app\\utils\\error_handler.py", line 196, in http_exception_handler\n    return create_error_response(\n           ^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\OneDrive\\Desktop\\OCR\\app\\utils\\error_handler.py", line 108, in create_error_response\n    return JSONResponse(\n           ^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\responses.py", line 182, in __init__\n    super().__init__(content, status_code, headers, media_type, background)\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\responses.py", line 45, in __init__\n    self.body = self.render(content)\n                ^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\starlette\\responses.py", line 185, in render\n    return json.dumps(\n           ^^^^^^^^^^^\n  File "C:\\Program Files\\Python312\\Lib\\json\\__init__.py", line 238, in dumps\n    **kw).encode(obj)\n          ^^^^^^^^^^^\n  File "C:\\Program Files\\Python312\\Lib\\json\\encoder.py", line 200, in encode\n    chunks = self.iterencode(o, _one_shot=True)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python312\\Lib\\json\\encoder.py", line 258, in iterencode\n    return _iterencode(o, 0)\n           ^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python312\\Lib\\json\\encoder.py", line 180, in default\n    raise TypeError(f\'Object of type {o.__class__.__name__} \'\nTypeError: Object of type datetime is not JSON serializable\n'} | {}
