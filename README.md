# Transport Pass OCR API

A comprehensive FastAPI-based OCR service for processing Transport Pass documents from the Department of Excise, Uttar Pradesh. This production-ready API provides document validation and structured data extraction capabilities using advanced OCR technologies.

## 🚀 Features

- **🔍 Document Validation**: Automatically validates if uploaded images are legitimate Transport Pass documents
- **🖼️ Image Preprocessing**: Handles blurry images, rotation correction, and various image formats (JPEG, PNG, BMP, TIFF)
- **📊 Structured Data Extraction**: Extracts both single-value fields and tabular data from documents
- **🤖 Multiple OCR Engines**: Uses Tesseract, EasyOCR, and PaddleOCR for optimal text recognition
- **🛡️ Production Ready**: Comprehensive error handling, logging, and API documentation
- **⚡ High Performance**: Optimized image processing pipeline with configurable parameters
- **📝 Comprehensive Logging**: Detailed logs for debugging and monitoring
- **🐳 Docker Support**: Easy deployment with Docker and Docker Compose

## 📋 Prerequisites

- Python 3.8+
- Tesseract OCR installed on your system
- At least 4GB RAM (recommended for OCR processing)
- Docker (optional, for containerized deployment)

### Installing Tesseract OCR

**Windows:**
```bash
# Download and install from: https://github.com/UB-Mannheim/tesseract/wiki
# Or using chocolatey:
choco install tesseract
```

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install tesseract-ocr tesseract-ocr-eng
```

**macOS:**
```bash
brew install tesseract
```

## 🛠️ Installation

### Method 1: Local Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd OCR
```

2. **Create virtual environment:**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

3. **Install dependencies:**
```bash
pip install -r requirements.txt
```

4. **Configure environment variables:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Run the application:**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Method 2: Docker Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd OCR
```

2. **Build and run with Docker Compose:**
```bash
docker-compose up --build
```

The API will be available at `http://localhost:8000`

## 🔧 Configuration

The application can be configured using environment variables in the `.env` file:

```env
# Application Configuration
APP_NAME=Transport Pass OCR API
APP_VERSION=1.0.0
DEBUG=False
LOG_LEVEL=INFO

# Server Configuration
HOST=0.0.0.0
PORT=8000

# OCR Configuration
TESSERACT_CMD_PATH=tesseract
OCR_CONFIDENCE_THRESHOLD=0.6
IMAGE_MAX_SIZE_MB=10

# Processing Configuration
MAX_WORKERS=4
PROCESSING_TIMEOUT_SECONDS=30

# Validation Thresholds
DOCUMENT_VALIDATION_CONFIDENCE_THRESHOLD=0.7
TEXT_SIMILARITY_THRESHOLD=0.8
```

## 📚 API Documentation

### Endpoints Overview

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Health check endpoint |
| `/validate-document` | POST | Validate Transport Pass document |
| `/extract-data` | POST | Extract structured data from document |

### Interactive Documentation

Once the server is running, visit:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

For detailed API documentation, see [API_DOCUMENTATION.md](API_DOCUMENTATION.md)

## 🏗️ Project Structure

```
OCR/
├── app/
│   ├── main.py              # FastAPI application entry point
│   ├── config.py            # Configuration settings
│   ├── models/              # Pydantic models
│   │   ├── requests.py      # Request models
│   │   └── responses.py     # Response models
│   ├── services/            # Business logic services
│   │   ├── document_validator.py  # Document validation logic
│   │   ├── data_extractor.py      # Data extraction logic
│   │   └── ocr_engine.py          # OCR engine integration
│   ├── utils/               # Utility functions
│   │   ├── image_processor.py     # Image preprocessing
│   │   ├── error_handler.py       # Error handling utilities
│   │   └── logging_config.py      # Logging configuration
│   └── tests/               # Test files
├── examples/                # Usage examples
├── logs/                    # Application logs
├── requirements.txt         # Python dependencies
├── Dockerfile              # Docker configuration
├── docker-compose.yml      # Docker Compose configuration
├── .env.example            # Environment variables template
└── README.md               # This file
```

## 🧪 Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest app/tests/test_main.py
```

## 📖 Usage Examples

### Python Client Example

```python
import requests

# Document validation
with open('transport_pass.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/validate-document',
        files={'file': f}
    )
    result = response.json()
    print(f"Valid: {result['isValid']}")

# Data extraction
with open('transport_pass.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/extract-data',
        files={'file': f}
    )
    result = response.json()
    print(f"Transport Pass No: {result['transportPassNo']}")
```

### cURL Example

```bash
# Document validation
curl -X POST "http://localhost:8000/validate-document" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@transport_pass.jpg"

# Data extraction
curl -X POST "http://localhost:8000/extract-data" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@transport_pass.jpg"
```

For more examples, see [examples/usage_example.py](examples/usage_example.py)

## 🚀 Deployment

### Production Deployment

1. **Set production environment variables:**
```env
DEBUG=False
LOG_LEVEL=WARNING
```

2. **Use a production WSGI server:**
```bash
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

3. **Set up reverse proxy (nginx example):**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Docker Production Deployment

```bash
# Build production image
docker build -t transport-pass-ocr:latest .

# Run with production settings
docker run -d \
  --name transport-pass-ocr \
  -p 8000:8000 \
  -e DEBUG=False \
  -e LOG_LEVEL=WARNING \
  -v $(pwd)/logs:/app/logs \
  transport-pass-ocr:latest
```

## 📊 Monitoring and Logging

The application generates comprehensive logs in the `logs/` directory:

- `app.log`: General application logs
- `errors.log`: Error-only logs
- `ocr.log`: OCR processing logs
- `api_access.log`: API access logs

Monitor the application health using the `/health` endpoint.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [API_DOCUMENTATION.md](API_DOCUMENTATION.md)
- **Examples**: [examples/](examples/)
- **Issues**: Create an issue in the project repository
- **Discussions**: Use the project discussions for questions and ideas

## 🙏 Acknowledgments

- [FastAPI](https://fastapi.tiangolo.com/) for the excellent web framework
- [Tesseract OCR](https://github.com/tesseract-ocr/tesseract) for OCR capabilities
- [EasyOCR](https://github.com/JaidedAI/EasyOCR) for additional OCR support
- [OpenCV](https://opencv.org/) for image processing
- [Loguru](https://github.com/Delgan/loguru) for beautiful logging
