"""
Main FastAPI application for Transport Pass OCR API.
"""

from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from contextlib import asynccontextmanager
import time
from loguru import logger

from app.config import get_settings, Settings
from app.models.requests import HealthResponse
from app.models.responses import ValidationResponse, ExtractionResponse, ErrorResponse
from app.utils.image_processor import ImageProcessor
from app.services.document_validator import DocumentValidator
from app.services.data_extractor import DataExtractor
from app.utils.error_handler import (
    APIError, ValidationError, FileError, OCRError, ProcessingError,
    api_error_handler, validation_error_handler, http_exception_handler,
    general_exception_handler, validate_file_upload
)
from app.utils.logging_config import setup_logging, log_api_access, log_validation_result, log_ocr_processing


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    settings = get_settings()
    setup_logging(settings.log_level)
    logger.info(f"Starting {settings.app_name} v{settings.app_version}")

    # Initialize services here if needed
    yield

    logger.info("Shutting down application")


# Create FastAPI application
def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="OCR API for processing Transport Pass documents from Department of Excise, Uttar Pradesh",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add error handlers
    app.add_exception_handler(APIError, api_error_handler)
    app.add_exception_handler(ValidationError, validation_error_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)

    return app


app = create_app()


@app.get("/health", response_model=HealthResponse, tags=["Health"])
async def health_check(settings: Settings = Depends(get_settings)):
    """Health check endpoint."""
    logger.info("Health check requested")
    return HealthResponse(
        status="healthy",
        message="Transport Pass OCR API is running",
        version=settings.app_version
    )


@app.post("/validate-document", response_model=ValidationResponse, tags=["Document Processing"])
async def validate_document(
    file: UploadFile = File(..., description="Image file to validate"),
    settings: Settings = Depends(get_settings)
):
    """
    Validate if the uploaded image is a legitimate Transport Pass document.

    This endpoint checks for:
    - Header: "DEPARTMENT OF EXCISE" and "UTTAR PRADESH"
    - Official government seal/logo
    - QR code or barcode
    - Document reference number
    - Structured sections with proper layout
    """
    try:
        logger.info(f"Document validation requested for file: {file.filename}")

        # Read file content
        file_content = await file.read()

        # Validate file
        validate_file_upload(file_content, file.filename)

        # Process image
        image_processor = ImageProcessor()
        processed_image = image_processor.preprocess_for_ocr(file_content)

        # Validate document
        validator = DocumentValidator()
        validation_result = validator.validate_document(processed_image)

        logger.info(f"Validation completed for {file.filename}: {validation_result.isValid}")
        return validation_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/extract-data", response_model=ExtractionResponse, tags=["Document Processing"])
async def extract_data(
    file: UploadFile = File(..., description="Image file to extract data from"),
    settings: Settings = Depends(get_settings)
):
    """
    Perform full OCR and structured data extraction from Transport Pass documents.

    Extracts both single-value fields and tabular data from validated documents.
    """
    try:
        logger.info(f"Data extraction requested for file: {file.filename}")

        # Read file content
        file_content = await file.read()

        # Validate file
        validate_file_upload(file_content, file.filename)

        # Process image
        image_processor = ImageProcessor()
        processed_image = image_processor.preprocess_for_ocr(file_content)

        # First validate the document
        validator = DocumentValidator()
        validation_result = validator.validate_document(processed_image)

        if not validation_result.isValid:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid document type: {validation_result.reason}"
            )

        # Extract data
        extractor = DataExtractor()
        extraction_result = extractor.extract_data(processed_image)

        # Validate extraction quality
        validation_info = extractor.validate_extracted_data(extraction_result)
        logger.info(f"Extraction quality score: {validation_info['quality_score']:.2f}")

        logger.info(f"Data extraction completed for {file.filename}")
        return extraction_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")





if __name__ == "__main__":
    import uvicorn
    settings = get_settings()
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
