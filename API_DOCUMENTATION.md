# Transport Pass OCR API Documentation

## Overview

The Transport Pass OCR API is a FastAPI-based service designed to process Transport Pass documents from the Department of Excise, Uttar Pradesh. The API provides document validation and structured data extraction capabilities using advanced OCR technologies.

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, the API does not require authentication. For production deployment, consider implementing appropriate authentication mechanisms.

## Endpoints

### 1. Health Check

**GET** `/health`

Check the health status of the API service.

#### Response

```json
{
  "status": "healthy",
  "message": "Transport Pass OCR API is running",
  "version": "1.0.0"
}
```

#### Status Codes
- `200 OK`: Service is healthy

---

### 2. Document Validation

**POST** `/validate-document`

Validates if an uploaded image is a legitimate Transport Pass document.

#### Request

**Content-Type:** `multipart/form-data`

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| file | File | Yes | Image file to validate (JPEG, PNG, BMP, TIFF) |

#### Response

```json
{
  "isValid": true,
  "documentType": "transport_pass",
  "confidence": 0.85,
  "reason": "Document contains required elements for Transport Pass",
  "detectedElements": [
    "DEPARTMENT OF EXCISE",
    "UTTAR PRADESH",
    "QR Code/Barcode",
    "Government Seal",
    "WHOLESALE10601-FL2B-15232"
  ]
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| isValid | boolean | Whether the document is a valid Transport Pass |
| documentType | string | Type of document detected (`transport_pass`, `invoice`, `other`) |
| confidence | number | Confidence score for classification (0-1) |
| reason | string | Brief explanation of classification |
| detectedElements | array | List of key elements found in document |

#### Status Codes
- `200 OK`: Validation completed successfully
- `400 Bad Request`: Invalid file type or file too large
- `422 Unprocessable Entity`: Processing error
- `500 Internal Server Error`: Server error

---

### 3. Data Extraction

**POST** `/extract-data`

Performs full OCR and structured data extraction from Transport Pass documents.

#### Request

**Content-Type:** `multipart/form-data`

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| file | File | Yes | Image file to extract data from (JPEG, PNG, BMP, TIFF) |

#### Response

```json
{
  "transportPassNo": "WHOLESALE10601-FL2B-15232",
  "indentNo": "IND123456",
  "gatePassDate": "15/01/2024",
  "unitDetailsUnitName": "Sample Unit Name",
  "unitDetailsLicenseType": "FL2B",
  "consigneeDetailsUnitName": "Sample Consignee",
  "routeDetails": "Sample Route Details",
  "majorRoute": "Major Route Sample",
  "distanceKms": "150",
  "totalRequestedCases": "100",
  "totalRequestedBottles": "2400",
  "totalDispatchedCases": "100",
  "totalDispatchedBottles": "2400",
  "indentDetails": [
    {
      "sNo": "1",
      "brandName": "Brand A",
      "description": "Premium Liquor",
      "packagingSize": "750ml",
      "packagingType": "Bottle",
      "qtyRequested": "50",
      "qtyDispatched": "50"
    }
  ]
}
```

#### Response Fields

**Single Value Fields:**
| Field | Type | Description |
|-------|------|-------------|
| transportPassNo | string | Transport Pass Number |
| indentNo | string | Indent Number |
| gatePassDate | string | Gate Pass Issue Date |
| unitDetailsUnitName | string | Unit Details - Unit Name |
| unitDetailsLicenseType | string | Unit Details - License Type |
| consigneeDetailsUnitName | string | Consignee Details - Unit Name |
| routeDetails | string | Route Details |
| majorRoute | string | Major Route |
| distanceKms | string | Distance in Kilometers |
| totalRequestedCases | string | Total Requested Cases |
| totalRequestedBottles | string | Total Requested Bottles |
| totalDispatchedCases | string | Total Dispatched Cases |
| totalDispatchedBottles | string | Total Dispatched Bottles |

**Table Data:**
| Field | Type | Description |
|-------|------|-------------|
| indentDetails | array | Array of indent detail objects |

**IndentDetail Object:**
| Field | Type | Description |
|-------|------|-------------|
| sNo | string | Serial Number |
| brandName | string | Brand Name |
| description | string | Product Description |
| packagingSize | string | Packaging Size |
| packagingType | string | Packaging Type |
| qtyRequested | string | Quantity Requested |
| qtyDispatched | string | Quantity Dispatched |

#### Status Codes
- `200 OK`: Data extraction completed successfully
- `400 Bad Request`: Invalid file type, file too large, or invalid document
- `422 Unprocessable Entity`: Processing error
- `500 Internal Server Error`: Server error

---

## Error Responses

All error responses follow this format:

```json
{
  "error": "ErrorType",
  "message": "Human-readable error message",
  "details": "Additional error details (only in debug mode)",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Common Error Types

| Error Type | Description |
|------------|-------------|
| ValidationError | Input validation failed |
| FileError | File-related issues (size, type, etc.) |
| ProcessingError | Document processing failed |
| OCRError | OCR processing failed |
| HTTPError | HTTP-related errors |
| InternalServerError | Unexpected server errors |

---

## File Upload Requirements

### Supported Formats
- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff, .tif)

### File Size Limits
- Maximum file size: 10 MB (configurable)

### Image Quality Recommendations
- Minimum resolution: 800x600 pixels
- Clear, well-lit images
- Minimal skew or rotation
- High contrast text

---

## Rate Limiting

Currently, no rate limiting is implemented. For production deployment, consider implementing appropriate rate limiting based on your requirements.

## Interactive Documentation

The API provides interactive documentation:

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## Example Usage

### Python Example

```python
import requests

# Document validation
with open('transport_pass.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/validate-document',
        files={'file': f}
    )
    validation_result = response.json()
    print(f"Valid: {validation_result['isValid']}")

# Data extraction
with open('transport_pass.jpg', 'rb') as f:
    response = requests.post(
        'http://localhost:8000/extract-data',
        files={'file': f}
    )
    extraction_result = response.json()
    print(f"Transport Pass No: {extraction_result['transportPassNo']}")
```

### cURL Example

```bash
# Document validation
curl -X POST "http://localhost:8000/validate-document" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@transport_pass.jpg"

# Data extraction
curl -X POST "http://localhost:8000/extract-data" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@transport_pass.jpg"
```

## Configuration

The API can be configured using environment variables. See `.env.example` for available configuration options.

## Logging

The API generates comprehensive logs:
- `logs/app.log`: General application logs
- `logs/errors.log`: Error-only logs
- `logs/ocr.log`: OCR processing logs
- `logs/api_access.log`: API access logs

## Support

For issues or questions, please refer to the project documentation or create an issue in the project repository.
