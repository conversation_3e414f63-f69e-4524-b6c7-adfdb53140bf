"""
Test cases for the main FastAPI application.
"""

import pytest
import asyncio
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import Mock, patch
import io
from PIL import Image
import numpy as np

from app.main import app
from app.config import get_settings


# Test client
client = TestClient(app)


class TestHealthEndpoint:
    """Test cases for the health endpoint."""
    
    def test_health_check(self):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "message" in data
        assert "version" in data


class TestValidateDocumentEndpoint:
    """Test cases for the validate-document endpoint."""
    
    def create_test_image(self, width=800, height=600, format="JPEG"):
        """Create a test image for upload."""
        # Create a simple test image
        image = Image.new('RGB', (width, height), color='white')
        
        # Add some text-like patterns
        import PIL.ImageDraw as ImageDraw
        draw = ImageDraw.Draw(image)
        draw.text((50, 50), "DEPARTMENT OF EXCISE", fill='black')
        draw.text((50, 100), "UTTAR PRADESH", fill='black')
        draw.text((50, 150), "TRANSPORT PASS", fill='black')
        
        # Convert to bytes
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format=format)
        img_byte_arr.seek(0)
        
        return img_byte_arr
    
    def test_validate_document_success(self):
        """Test successful document validation."""
        test_image = self.create_test_image()
        
        with patch('app.utils.image_processor.ImageProcessor.preprocess_for_ocr') as mock_preprocess, \
             patch('app.services.document_validator.DocumentValidator.validate_document') as mock_validate:
            
            # Mock the preprocessing
            mock_preprocess.return_value = np.zeros((600, 800, 3), dtype=np.uint8)
            
            # Mock the validation result
            from app.models.responses import ValidationResponse
            mock_validate.return_value = ValidationResponse(
                isValid=True,
                documentType="transport_pass",
                confidence=0.85,
                reason="Document contains required elements",
                detectedElements=["DEPARTMENT OF EXCISE", "UTTAR PRADESH"]
            )
            
            response = client.post(
                "/validate-document",
                files={"file": ("test.jpg", test_image, "image/jpeg")}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["isValid"] is True
            assert data["documentType"] == "transport_pass"
            assert data["confidence"] == 0.85
    
    def test_validate_document_invalid_file_type(self):
        """Test validation with invalid file type."""
        # Create a text file instead of image
        text_content = io.BytesIO(b"This is not an image")
        
        response = client.post(
            "/validate-document",
            files={"file": ("test.txt", text_content, "text/plain")}
        )
        
        assert response.status_code == 400
        assert "Unsupported file type" in response.json()["message"]
    
    def test_validate_document_large_file(self):
        """Test validation with file too large."""
        # Create a large image (simulate by mocking file size check)
        test_image = self.create_test_image()
        
        with patch('app.utils.error_handler.validate_file_upload') as mock_validate:
            from app.utils.error_handler import FileError
            mock_validate.side_effect = FileError("File too large")
            
            response = client.post(
                "/validate-document",
                files={"file": ("test.jpg", test_image, "image/jpeg")}
            )
            
            assert response.status_code == 400
            assert "File too large" in response.json()["message"]


class TestExtractDataEndpoint:
    """Test cases for the extract-data endpoint."""
    
    def create_test_image(self):
        """Create a test image for upload."""
        image = Image.new('RGB', (800, 600), color='white')
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='JPEG')
        img_byte_arr.seek(0)
        return img_byte_arr
    
    def test_extract_data_success(self):
        """Test successful data extraction."""
        test_image = self.create_test_image()
        
        with patch('app.utils.image_processor.ImageProcessor.preprocess_for_ocr') as mock_preprocess, \
             patch('app.services.document_validator.DocumentValidator.validate_document') as mock_validate, \
             patch('app.services.data_extractor.DataExtractor.extract_data') as mock_extract:
            
            # Mock preprocessing
            mock_preprocess.return_value = np.zeros((600, 800, 3), dtype=np.uint8)
            
            # Mock validation
            from app.models.responses import ValidationResponse
            mock_validate.return_value = ValidationResponse(
                isValid=True,
                documentType="transport_pass",
                confidence=0.85,
                reason="Valid document",
                detectedElements=[]
            )
            
            # Mock extraction
            from app.models.responses import ExtractionResponse
            mock_extract.return_value = ExtractionResponse(
                transportPassNo="WHOLESALE10601-FL2B-15232",
                indentNo="IND123456",
                gatePassDate="2024-01-15"
            )
            
            response = client.post(
                "/extract-data",
                files={"file": ("test.jpg", test_image, "image/jpeg")}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["transportPassNo"] == "WHOLESALE10601-FL2B-15232"
            assert data["indentNo"] == "IND123456"
    
    def test_extract_data_invalid_document(self):
        """Test data extraction with invalid document."""
        test_image = self.create_test_image()
        
        with patch('app.utils.image_processor.ImageProcessor.preprocess_for_ocr') as mock_preprocess, \
             patch('app.services.document_validator.DocumentValidator.validate_document') as mock_validate:
            
            # Mock preprocessing
            mock_preprocess.return_value = np.zeros((600, 800, 3), dtype=np.uint8)
            
            # Mock validation failure
            from app.models.responses import ValidationResponse
            mock_validate.return_value = ValidationResponse(
                isValid=False,
                documentType="other",
                confidence=0.3,
                reason="Not a valid Transport Pass",
                detectedElements=[]
            )
            
            response = client.post(
                "/extract-data",
                files={"file": ("test.jpg", test_image, "image/jpeg")}
            )
            
            assert response.status_code == 400
            assert "Invalid document type" in response.json()["message"]


class TestErrorHandling:
    """Test cases for error handling."""
    
    def test_404_endpoint(self):
        """Test 404 error for non-existent endpoint."""
        response = client.get("/non-existent-endpoint")
        assert response.status_code == 404
    
    def test_method_not_allowed(self):
        """Test method not allowed error."""
        response = client.get("/validate-document")  # Should be POST
        assert response.status_code == 405


if __name__ == "__main__":
    pytest.main([__file__])
