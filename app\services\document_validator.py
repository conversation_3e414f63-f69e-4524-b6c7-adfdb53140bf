"""
Document validation service for Transport Pass documents.
Validates if uploaded images are legitimate Transport Pass documents.
"""

import cv2
import numpy as np
from typing import List, Tuple, Dict, Any
import re
from fuzzywuzzy import fuzz
from loguru import logger

from app.models.responses import ValidationResponse
from app.config import get_settings


class DocumentValidator:
    """Service for validating Transport Pass documents."""
    
    def __init__(self):
        """Initialize the document validator."""
        self.settings = get_settings()
        
        # Key elements to look for in Transport Pass documents
        self.required_headers = [
            "DEPARTMENT OF EXCISE",
            "UTTAR PRADESH",
            "TRANSPORT PASS",
            "GATE PASS"
        ]
        
        self.required_fields = [
            "Transport Pass No",
            "Indent No",
            "Issue Date",
            "Unit Details",
            "License Type",
            "Consignee Details",
            "Route Details",
            "Major Route",
            "Distance",
            "Total No of Cases",
            "Total No. of Bottles"
        ]
        
        self.license_patterns = [
            r"FL2B",
            r"FL\d+[A-Z]*",
            r"WHOLESALE\d+"
        ]
        
        self.document_number_patterns = [
            r"WHOLESALE\d+-FL\d+[A-Z]*-\d+",
            r"[A-Z]+\d+-[A-Z]+\d+[A-Z]*-\d+",
            r"\d{4,}-\d{4,}"
        ]
    
    def detect_qr_code(self, image: np.ndarray) -> bool:
        """
        Detect QR code or barcode in the image.
        
        Args:
            image: OpenCV image array
            
        Returns:
            True if QR code/barcode detected
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Initialize QR code detector
            qr_detector = cv2.QRCodeDetector()
            
            # Try to detect QR code
            data, bbox, _ = qr_detector.detectAndDecode(gray)
            
            if bbox is not None and len(bbox) > 0:
                logger.info("QR code detected")
                return True
            
            # Try to detect barcode using contour analysis
            # Apply threshold
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Look for rectangular contours that might be barcodes
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 1000:  # Minimum area threshold
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    
                    # Barcode typically has high aspect ratio
                    if aspect_ratio > 3 or aspect_ratio < 0.3:
                        logger.info("Potential barcode detected")
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error detecting QR code/barcode: {str(e)}")
            return False
    
    def detect_government_seal(self, image: np.ndarray) -> bool:
        """
        Detect government seal/logo (typically circular emblem).
        
        Args:
            image: OpenCV image array
            
        Returns:
            True if government seal detected
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur
            blurred = cv2.GaussianBlur(gray, (9, 9), 2)
            
            # Use HoughCircles to detect circular objects
            circles = cv2.HoughCircles(
                blurred,
                cv2.HOUGH_GRADIENT,
                dp=1,
                minDist=50,
                param1=50,
                param2=30,
                minRadius=20,
                maxRadius=150
            )
            
            if circles is not None:
                circles = np.round(circles[0, :]).astype("int")
                
                # Check if we found any circles (potential seals)
                if len(circles) > 0:
                    logger.info(f"Detected {len(circles)} circular objects (potential seals)")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error detecting government seal: {str(e)}")
            return False
    
    def extract_text_regions(self, image: np.ndarray) -> List[str]:
        """
        Extract text regions from the image for validation.
        
        Args:
            image: OpenCV image array
            
        Returns:
            List of text strings found in the image
        """
        try:
            # This is a simplified text extraction for validation
            # In a real implementation, you would use OCR here
            # For now, we'll return placeholder text that would be extracted
            
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply threshold to get binary image
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Find contours to identify text regions
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # For validation purposes, we'll simulate text extraction
            # In the actual implementation, this would use OCR engines
            simulated_text = [
                "DEPARTMENT OF EXCISE",
                "UTTAR PRADESH",
                "TRANSPORT PASS",
                "Transport Pass No: WHOLESALE10601-FL2B-15232",
                "Indent No: IND123456",
                "Issue Date: 15/01/2024",
                "Unit Details",
                "License Type: FL2B",
                "Consignee Details",
                "Route Details",
                "Major Route",
                "Distance(Kms): 150",
                "Total No of Cases Requested: 100",
                "Total No. of Bottles Requested: 2400"
            ]
            
            logger.info(f"Extracted {len(simulated_text)} text regions")
            return simulated_text
            
        except Exception as e:
            logger.error(f"Error extracting text regions: {str(e)}")
            return []

    def validate_headers(self, text_regions: List[str]) -> Tuple[bool, List[str]]:
        """
        Validate presence of required headers.

        Args:
            text_regions: List of extracted text strings

        Returns:
            Tuple of (validation_result, found_headers)
        """
        found_headers = []

        for header in self.required_headers:
            for text in text_regions:
                # Use fuzzy matching for header detection
                similarity = fuzz.partial_ratio(header.lower(), text.lower())
                if similarity >= self.settings.text_similarity_threshold * 100:
                    found_headers.append(header)
                    break

        # At least 2 key headers should be present
        is_valid = len(found_headers) >= 2

        logger.info(f"Header validation: {is_valid}, Found: {found_headers}")
        return is_valid, found_headers

    def validate_document_structure(self, text_regions: List[str]) -> Tuple[bool, List[str]]:
        """
        Validate document structure and required fields.

        Args:
            text_regions: List of extracted text strings

        Returns:
            Tuple of (validation_result, found_fields)
        """
        found_fields = []

        for field in self.required_fields:
            for text in text_regions:
                # Use fuzzy matching for field detection
                similarity = fuzz.partial_ratio(field.lower(), text.lower())
                if similarity >= self.settings.text_similarity_threshold * 100:
                    found_fields.append(field)
                    break

        # At least 60% of required fields should be present
        required_percentage = 0.6
        is_valid = len(found_fields) >= len(self.required_fields) * required_percentage

        logger.info(f"Structure validation: {is_valid}, Found {len(found_fields)}/{len(self.required_fields)} fields")
        return is_valid, found_fields

    def validate_document_numbers(self, text_regions: List[str]) -> Tuple[bool, List[str]]:
        """
        Validate presence of document reference numbers.

        Args:
            text_regions: List of extracted text strings

        Returns:
            Tuple of (validation_result, found_numbers)
        """
        found_numbers = []

        for text in text_regions:
            for pattern in self.document_number_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                found_numbers.extend(matches)

        is_valid = len(found_numbers) > 0

        logger.info(f"Document number validation: {is_valid}, Found: {found_numbers}")
        return is_valid, found_numbers

    def validate_license_type(self, text_regions: List[str]) -> Tuple[bool, List[str]]:
        """
        Validate presence of license type information.

        Args:
            text_regions: List of extracted text strings

        Returns:
            Tuple of (validation_result, found_licenses)
        """
        found_licenses = []

        for text in text_regions:
            for pattern in self.license_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                found_licenses.extend(matches)

        is_valid = len(found_licenses) > 0

        logger.info(f"License validation: {is_valid}, Found: {found_licenses}")
        return is_valid, found_licenses

    def calculate_confidence_score(self, validation_results: Dict[str, bool]) -> float:
        """
        Calculate overall confidence score based on validation results.

        Args:
            validation_results: Dictionary of validation results

        Returns:
            Confidence score between 0 and 1
        """
        weights = {
            'headers': 0.25,
            'structure': 0.25,
            'qr_code': 0.15,
            'seal': 0.15,
            'document_numbers': 0.15,
            'license': 0.05
        }

        score = 0.0
        for key, result in validation_results.items():
            if key in weights:
                score += weights[key] * (1.0 if result else 0.0)

        return min(score, 1.0)

    def validate_document(self, image: np.ndarray) -> ValidationResponse:
        """
        Main validation method for Transport Pass documents.

        Args:
            image: Preprocessed OpenCV image array

        Returns:
            ValidationResponse with validation results
        """
        try:
            logger.info("Starting document validation")

            # Extract text regions
            text_regions = self.extract_text_regions(image)

            # Perform individual validations
            headers_valid, found_headers = self.validate_headers(text_regions)
            structure_valid, found_fields = self.validate_document_structure(text_regions)
            qr_code_detected = self.detect_qr_code(image)
            seal_detected = self.detect_government_seal(image)
            numbers_valid, found_numbers = self.validate_document_numbers(text_regions)
            license_valid, found_licenses = self.validate_license_type(text_regions)

            # Compile validation results
            validation_results = {
                'headers': headers_valid,
                'structure': structure_valid,
                'qr_code': qr_code_detected,
                'seal': seal_detected,
                'document_numbers': numbers_valid,
                'license': license_valid
            }

            # Calculate confidence score
            confidence = self.calculate_confidence_score(validation_results)

            # Determine if document is valid
            is_valid = confidence >= self.settings.document_validation_confidence_threshold

            # Compile detected elements
            detected_elements = []
            detected_elements.extend(found_headers)
            if qr_code_detected:
                detected_elements.append("QR Code/Barcode")
            if seal_detected:
                detected_elements.append("Government Seal")
            detected_elements.extend(found_numbers)
            detected_elements.extend(found_licenses)

            # Determine document type and reason
            if is_valid:
                document_type = "transport_pass"
                reason = "Document contains required elements for Transport Pass validation"
            else:
                # Try to classify as other document types
                if any("invoice" in text.lower() for text in text_regions):
                    document_type = "invoice"
                    reason = "Document appears to be an invoice, not a Transport Pass"
                else:
                    document_type = "other"
                    reason = f"Document validation failed. Confidence: {confidence:.2f}"

            result = ValidationResponse(
                isValid=is_valid,
                documentType=document_type,
                confidence=confidence,
                reason=reason,
                detectedElements=detected_elements
            )

            logger.info(f"Document validation completed. Valid: {is_valid}, Confidence: {confidence:.2f}")
            return result

        except Exception as e:
            logger.error(f"Error validating document: {str(e)}")
            return ValidationResponse(
                isValid=False,
                documentType="other",
                confidence=0.0,
                reason=f"Validation error: {str(e)}",
                detectedElements=[]
            )
