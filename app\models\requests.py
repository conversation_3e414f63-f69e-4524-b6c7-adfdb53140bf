"""
Request models for the Transport Pass OCR API.
"""

from pydantic import BaseModel, Field
from typing import Optional
from fastapi import UploadFile


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str = "healthy"
    message: str = "Transport Pass OCR API is running"
    version: str = "1.0.0"


class ValidationRequest(BaseModel):
    """Request model for document validation (handled via multipart form)."""
    pass  # File will be handled as UploadFile in the endpoint


class ExtractionRequest(BaseModel):
    """Request model for data extraction (handled via multipart form)."""
    pass  # File will be handled as UploadFile in the endpoint
